<template>
  <!-- 入院24小时内营养风险筛查病人查询 -->
  <div class="container">
    <div class="filter-container">
      <div class="filter-col">
        <!-- 医生用户：专科选择 -->
        <div v-if="userType === '01'" class="filter-item">
          <span class="filter-label">专科：</span>
          <el-select
            v-model="selectedZhuanKe"
            placeholder="请选择专科"
            style="width: 200px"
            size="mini"
          >
            <el-option
              v-for="item in zhuanKeList"
              :key="item.daiMa"
              :label="item.mingCheng"
              :value="item.daiMa"
            ></el-option>
          </el-select>
        </div>

        <!-- 护士用户：点评类型选择 -->
        <div v-if="userType === '04'" class="filter-item">
          <span class="filter-label">点评类型：</span>
          <el-radio-group v-model="commentType" size="mini">
            <el-radio :label="1">护士长点评</el-radio>
            <el-radio :label="2">护士长交叉点评</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="filter-col">
        <div class="filter-item">
          <el-button type="primary" size="mini" class="purple-button" @click="handleQuery">
            查询
          </el-button>
          <el-button type="success" size="mini" @click="exportToExcel">导出Excel</el-button>
        </div>
      </div>
    </div>

    <!-- 数据展示区域 -->
    <div class="table-container">
      <div class="title">
        <span>{{ pageTitle }}</span>
        <span class="total-count">共{{ tableData.length }}条数据</span>
      </div>
      <el-table
        :data="tableData"
        border
        stripe
        style="width: 100%"
        height="calc(100% - 30px)"
        size="mini"
        v-loading="loading"
      >
        <el-table-column prop="zhuanKeMC" label="专科" width="120"></el-table-column>
        <el-table-column prop="bingAnHao" label="病案号" width="120"></el-table-column>
        <el-table-column prop="xingMing" label="姓名" width="100"></el-table-column>
        <el-table-column prop="xingBie" label="性别" width="80"></el-table-column>
        <el-table-column prop="bingQuMC" label="病区" width="120"></el-table-column>
        <el-table-column prop="chuangWeiHao" label="床位号" width="80"></el-table-column>
        <el-table-column prop="shaiChaLX" label="筛查类型" width="120"></el-table-column>
        <el-table-column prop="zongPingFen" label="总评分" width="80"></el-table-column>
        <el-table-column prop="pingGuRenYuan" label="评估人员姓名" width="120"></el-table-column>
        <el-table-column prop="pingGuRiQi" label="评估日期" width="120"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getUnCommentListByYSZ, getUnCommentListByHSZ, getUnCommentListByJCDP, getZhuanKeList } from '@/api/medical-quality'
import { mapState } from 'vuex'
import { format } from 'date-fns'
import * as XLSX from 'xlsx'

export default {
  name: 'Admission24hScreeningQuery',
  data() {
    return {
      // 用户类型
      userType: '',
      // 查询参数
      queryParams: {},
      // 专科列表（医生用户）
      zhuanKeList: [],
      selectedZhuanKe: '',
      // 点评类型（护士用户）
      commentType: 1, // 1-护士长点评，2-护士长交叉点评
      // 表格数据
      tableData: [],
      // 加载状态
      loading: false
    }
  },
  computed: {
    ...mapState({
      doctorInfo: ({ patient }) => patient.doctorInfo,
      bingQuID: ({ patient }) => patient.bingQuID
    }),
    // 页面标题
    pageTitle() {
      if (this.userType === '01') {
        return '入院24小时内营养风险筛查病人查询'
      } else if (this.userType === '04') {
        return '本病区需点评营养风险筛查表列表'
      }
      return '营养风险筛查查询'
    }
  },
  created() {
    this.init()
  },
  methods: {
    // 初始化
    async init() {
      // 获取用户类型
      this.userType = this.doctorInfo.renYuanLB || '01'

      if (this.userType === '01') {
        // 医生用户：获取专科列表
        await this.getZhuanKeListData()
      } else if (this.userType === '04') {
        // 护士用户：设置默认点评类型
        this.commentType = 1
      }

      // 初始查询
      await this.handleQuery()
    },

    // 获取专科列表
    async getZhuanKeListData() {
      try {
        const res = await getZhuanKeList()
        if (res.hasError === 0 && res.data) {
          this.zhuanKeList = res.data
          // 设置默认选中第一个专科
          if (this.zhuanKeList.length > 0) {
            this.selectedZhuanKe = this.zhuanKeList[0].daiMa
          }
        }
      } catch (error) {
        console.error('获取专科列表失败:', error)
        this.$message.error('获取专科列表失败')
      }
    },

    // 查询数据
    async handleQuery() {
      if (this.userType === '01' && !this.selectedZhuanKe) {
        this.$message.warning('请选择专科')
        return
      }

      try {
        this.loading = true
        let res = null

        if (this.userType === '01') {
          // 医生查询
          res = await getUnCommentListByYSZ({
            zhuanKeID: this.selectedZhuanKe
          })
        } else if (this.userType === '04') {
          // 护士查询
          const params = {
            bingQuID: this.bingQuID
          }

          if (this.commentType === 1) {
            // 护士长点评
            res = await getUnCommentListByHSZ(params)
          } else if (this.commentType === 2) {
            // 护士长交叉点评
            res = await getUnCommentListByJCDP(params)
          }
        }

        this.loading = false

        if (res && res.hasError === 0 && res.data) {
          this.tableData = res.data || []
        } else {
          this.tableData = []
          if (res && res.errorMessage) {
            this.$message.error(res.errorMessage)
          }
        }
      } catch (error) {
        this.loading = false
        console.error('查询数据失败:', error)
        this.$message.error(error.errorMessage || '查询数据失败')
        this.tableData = []
      }
    },

    // 导出Excel
    exportToExcel() {
      if (this.tableData.length === 0) {
        this.$message.warning('暂无数据可导出')
        return
      }

      try {
        // 准备导出数据
        const exportData = this.tableData.map((item) => {
          return {
            专科: item.zhuanKeMC || '',
            病案号: item.bingAnHao || '',
            姓名: item.xingMing || '',
            性别: item.xingBie || '',
            病区: item.bingQuMC || '',
            床位号: item.chuangWeiHao || '',
            筛查类型: item.shaiChaLX || '',
            总评分: item.zongPingFen || '',
            评估人员姓名: item.pingGuRenYuan || '',
            评估日期: item.pingGuRiQi || ''
          }
        })

        // 创建工作表
        const worksheet = XLSX.utils.json_to_sheet(exportData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

        // 生成文件名
        const fileName = `${this.pageTitle}_${format(new Date(), 'yyyy-MM-dd')}.xlsx`

        // 下载文件
        XLSX.writeFile(workbook, fileName)
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
  padding: 10px;
  background: #fff;
}

.filter-container {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  display: flex;
}

.filter-col {
  display: flex;
  padding: 0 8px;

  .filter-item {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.filter-label {
  font-weight: bold;
  min-width: 80px;
}

.purple-button {
  background: #a66dd4;
  border: 1px solid #a66dd4;
  &:hover,
  &:focus {
    background: #ce8be0;
    border-color: #ce8be0;
  }
}

.table-container {
  height: calc(100% - 62px);
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  padding: 10px;

  .title {
    height: 20px;
    margin-bottom: 10px;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .total-count {
    margin-left: auto;
    margin-right: auto;
  }
}
</style>
